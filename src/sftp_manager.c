#include "sftp_manager.h"
#include "update.h"
#include "core/logger.h"
#include <libssh2.h>
#include <libssh2_sftp.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <unistd.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <openssl/evp.h> 

// Structure interne pour la session SFTP
struct SftpSession {
    int sock;
    LIBSSH2_SESSION *ssh_session;
    LIBSSH2_SFTP *sftp_session;
    SftpConfig config;
};

// Variables globales pour l'initialisation de libssh2
static bool libssh2_initialized = false;

/**
 * @brief Initialise libssh2 si nécessaire
 */
static int init_libssh2() {
    logger_log(LOG_DEBUG, "sftp_manager.c : Initialisation de libssh2...");
    if (!libssh2_initialized) {
        if (libssh2_init(0) != 0) {
            logger_log(LOG_ERROR, "sftp_manager.c : Échec de l'initialisation de libssh2");
            return -1;
        }
        libssh2_initialized = true;
        logger_log(LOG_DEBUG, "sftp_manager.c : libssh2 initialisé");
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : libssh2 déjà initialisé. aucune action effectuée");
    }
    return 0;
}

/**
 * @brief Calcule le SHA-256 du fichier téléchargé pour vérification d'intégrité (version OpenSSL 3.0+)
 */
static int compute_sha256(const char *file_path, char *output_hex) {
    unsigned char buffer[8192];
    unsigned char sha256_digest[EVP_MAX_MD_SIZE];
    unsigned int sha256_digest_len;
    
    FILE *f = fopen(file_path, "rb");
    logger_log(LOG_DEBUG, "sftp_manager.c : compute_sha256 - Ouverture du fichier %s", file_path);
    if (!f) return -1;

    // Utilisation de l'API EVP moderne
    EVP_MD_CTX *ctx = EVP_MD_CTX_new();
    if (!ctx) {
        fclose(f);
        return -1;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : EVP_MD_CTX créé pour le calcul du SHA-256");
    }

    if (EVP_DigestInit_ex(ctx, EVP_sha256(), NULL) != 1) {
        EVP_MD_CTX_free(ctx);
        fclose(f);
        return -1;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : EVP_MD_CTX initialisé pour le SHA-256");
    }

    size_t bytes;
    logger_log(LOG_DEBUG, "sftp_manager.c : Lecture du fichier %s pour le SHA-256", file_path);
    while ((bytes = fread(buffer, 1, sizeof(buffer), f)) != 0) {
        if (EVP_DigestUpdate(ctx, buffer, bytes) != 1) {
            EVP_MD_CTX_free(ctx);
            fclose(f);
            return -1;
        }
    }
    fclose(f);

    if (EVP_DigestFinal_ex(ctx, sha256_digest, &sha256_digest_len) != 1) {
        logger_log(LOG_ERROR, "sftp_manager.c : Échec de la finalisation du digest SHA-256 pour %s", file_path);
        EVP_MD_CTX_free(ctx);
        return -1;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : SHA-256 calculé avec succès pour %s", file_path);
    }

    EVP_MD_CTX_free(ctx);

    // Convertir en hexadécimal (SHA-256 fait toujours 32 bytes = 64 caractères hex)
    logger_log(LOG_DEBUG, "sftp_manager.c : Conversion du SHA-256 en hexadécimal pour %s", file_path);
    for (unsigned int i = 0; i < sha256_digest_len && i < 32; i++) {
        sprintf(&output_hex[i * 2], "%02x", sha256_digest[i]);
    }
    output_hex[64] = '\0';  // SHA-256 = toujours 64 caractères hex + \0

    logger_log(LOG_DEBUG, "sftp_manager.c : SHA-256 en hexadécimal pour %s : %s", file_path, output_hex);
    return 0;
}

/**
 * @brief Contrôle l'intégrité du fichier avec SHA-256
 */
int sftp_verify_sha256(SftpSession *session, const char *remote_path, const char *local_path) {
    char remote_sha256_path[1024];
    char local_sha256_path[1024];
    logger_log(LOG_DEBUG, "sftp_manager.c : Vérification du hash SHA-256 pour %s...", local_path);

    snprintf(remote_sha256_path, sizeof(remote_sha256_path), "%s.sha256", remote_path);
    snprintf(local_sha256_path, sizeof(local_sha256_path), "%s.sha256", local_path);

    // Vérifier si le .sha256 existe sur le serveur
    logger_log(LOG_DEBUG, "sftp_manager.c : Vérification de l'existence du fichier SHA-256 sur le serveur %s", remote_sha256_path);
    if (!sftp_file_exists(session, remote_sha256_path)) {
        logger_log(LOG_ERROR, "sftp_manager.c : Fichier SHA-256 non trouvé sur le serveur pour %s", remote_path);
        goto error_cleanup;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Fichier SHA-256 trouvé sur le serveur");
    }

    // Télécharger le .sha256
    logger_log(LOG_DEBUG, "sftp_manager.c : Téléchargement du fichier SHA-256 depuis %s vers %s", remote_sha256_path, local_sha256_path);
    if (sftp_download_file(session, remote_sha256_path, local_sha256_path) != 0) {
        logger_log(LOG_ERROR, "sftp_manager.c : Impossible de télécharger le SHA-256 pour %s", remote_path);
        goto error_cleanup;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Fichier SHA-256 téléchargé avec succès vers %s", local_sha256_path);
    }

    // Lire le SHA-256 attendu
    FILE *sha256_file = fopen(local_sha256_path, "r");
    logger_log(LOG_DEBUG, "sftp_manager.c : Ouverture du fichier SHA-256 local %s", local_sha256_path);
    if (!sha256_file) {
        logger_log(LOG_ERROR, "sftp_manager.c : Impossible d’ouvrir le fichier SHA-256 local %s", local_sha256_path);
        goto error_cleanup;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Fichier SHA-256 local ouvert avec succès");
    }

    char expected_sha256[65];  // SHA-256 = 64 caractères hex + '\0'
    logger_log(LOG_DEBUG, "sftp_manager.c : Lecture du hash SHA-256 attendu depuis %s", local_sha256_path);
    if (fscanf(sha256_file, "%64s", expected_sha256) != 1) {
        fclose(sha256_file);
        logger_log(LOG_ERROR, "sftp_manager.c : Format du fichier SHA-256 invalide %s", local_sha256_path);
        goto error_cleanup;
    } else {
        expected_sha256[64] = '\0';  // Assurer la terminaison de chaîne
        logger_log(LOG_DEBUG, "sftp_manager.c : Hash SHA-256 attendu lu: %s", expected_sha256);
    }
    fclose(sha256_file);

    // Calculer le SHA-256 local
    char computed_sha256[65];  // SHA-256 = 64 caractères hex + '\0'
    logger_log(LOG_DEBUG, "sftp_manager.c : Calcul du hash SHA-256 pour %s...", local_path);
    if (compute_sha256(local_path, computed_sha256) != 0) {
        logger_log(LOG_ERROR, "sftp_manager.c : Échec du calcul SHA-256 pour %s", local_path);
        goto error_cleanup;
    } else {
        computed_sha256[64] = '\0';  // Assurer la terminaison de chaîne
        logger_log(LOG_DEBUG, "sftp_manager.c : Hash SHA-256 calculé: %s", computed_sha256);
    }

    // Comparer
    logger_log(LOG_DEBUG, "sftp_manager.c : Comparaison des hashes SHA-256 pour %s...", local_path);
    if (strcasecmp(expected_sha256, computed_sha256) != 0) {
        logger_log(LOG_ERROR, "sftp_manager.c : SHA-256 mismatch pour %s (attendu: %s, obtenu: %s)",
                   local_path, expected_sha256, computed_sha256);
        goto error_cleanup;
    }

    logger_log(LOG_INFO, "sftp_manager.c : SHA-256 vérifié avec succès pour %s", local_path);
    return 0;

error_cleanup:
    // Supprimer le fichier principal et le fichier .sha256 si présents
    logger_log(LOG_DEBUG, "sftp_manager.c : Nettoyage des fichiers temporaires...");
    if (unlink(local_path) == 0) {
        logger_log(LOG_WARNING, "sftp_manager.c : Fichier corrompu supprimé : %s", local_path);
    }
    if (unlink(local_sha256_path) == 0) {
        logger_log(LOG_WARNING, "sftp_manager.c : Fichier SHA-256 supprimé : %s", local_sha256_path);
    }
    logger_log(LOG_ERROR, "sftp_manager.c : fin du nettoyage des fichiers temporaires");
    return -1;
}

/**
 * @brief Résout un nom d'hôte en adresse IP
 */
static int resolve_hostname(const char *hostname, char *ip_str, size_t ip_str_size) {
    struct hostent *host_entry;
    struct in_addr addr;
    
    // Vérifier si c'est déjà une adresse IP
    logger_log(LOG_DEBUG, "sftp_manager.c : Vérification si %s est une adresse IP...", hostname);
    if (inet_aton(hostname, &addr)) {
        strncpy(ip_str, hostname, ip_str_size - 1);
        ip_str[ip_str_size - 1] = '\0';
        logger_log(LOG_DEBUG, "sftp_manager.c : %s est une adresse IP valide", hostname);
        return 0;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : %s n'est pas une adresse IP, résolution DNS nécessaire", hostname);
    }
    
    // Résoudre le nom d'hôte
    host_entry = gethostbyname(hostname);
    logger_log(LOG_DEBUG, "sftp_manager.c : Résolution DNS en cours pour %s...", hostname);
    if (host_entry == NULL) {
        logger_log(LOG_ERROR, "sftp_manager.c : Impossible de résoudre l'hôte %s", hostname);
        return -1;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Résolution DNS réussie pour %s", hostname);
    }

    addr.s_addr = *((unsigned long *)host_entry->h_addr_list[0]);
    strncpy(ip_str, inet_ntoa(addr), ip_str_size - 1);
    ip_str[ip_str_size - 1] = '\0';
    
    logger_log(LOG_DEBUG, "sftp_manager.c : %s résolu vers %s", hostname, ip_str);
    return 0;
}

/**
 * @brief Établit une connexion TCP vers le serveur
 */
static int connect_to_server(const char *host, int port, int timeout) {
    char ip_str[INET_ADDRSTRLEN];
    
    logger_log(LOG_DEBUG, "sftp_manager.c : Résolution de l'hôte %s...", host);
    if (resolve_hostname(host, ip_str, sizeof(ip_str)) != 0) {
        return -1;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Hôte %s résolu en %s", host, ip_str);
    }
    
    int sock = socket(AF_INET, SOCK_STREAM, 0);
    logger_log(LOG_DEBUG, "sftp_manager.c : Création du socket...");
    if (sock == -1) {
        logger_log(LOG_ERROR, "sftp_manager.c : Impossible de créer le socket: %s", strerror(errno));
        return -1;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Socket créé avec succès");
    }
    
    // Configuration du timeout
    struct timeval tv;
    tv.tv_sec = timeout;
    tv.tv_usec = 0;
    setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, (const char*)&tv, sizeof(tv));
    setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, (const char*)&tv, sizeof(tv));
    logger_log(LOG_DEBUG, "sftp_manager.c : Timeout configuré pour le socket");

    struct sockaddr_in sin;
    sin.sin_family = AF_INET;
    sin.sin_port = htons(port);
    sin.sin_addr.s_addr = inet_addr(ip_str);
    logger_log(LOG_DEBUG, "sftp_manager.c : Adresse de connexion configurée pour %s:%d", ip_str, port);

    logger_log(LOG_DEBUG, "sftp_manager.c : Connexion à %s:%d...", ip_str, port);
    if (connect(sock, (struct sockaddr*)(&sin), sizeof(struct sockaddr_in)) != 0) {
        logger_log(LOG_ERROR, "sftp_manager.c : Échec de connexion à %s:%d: %s", ip_str, port, strerror(errno));
        close(sock);
        return -1;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Connexion à %s:%d réussie", ip_str, port);
    }
    
    logger_log(LOG_INFO, "sftp_manager.c : Connexion TCP établie vers %s:%d", ip_str, port);
    return sock;
}

SftpSession* sftp_session_init(const SftpConfig *config) {

    if (init_libssh2() != 0) {
        logger_log(LOG_ERROR, "sftp_manager.c : Échec de l'initialisation de libssh2");
        return NULL;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : libssh2 initialisé pour la session SFTP");
    }
    
    SftpSession *session = calloc(1, sizeof(SftpSession));
    if (!session) {
        logger_log(LOG_ERROR, "sftp_manager.c : Allocation mémoire échouée pour la session");
        return NULL;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Session SFTP allouée");
    }
    
    // Copie de la configuration
    logger_log(LOG_DEBUG, "sftp_manager.c : Copie de la configuration SFTP");
    memcpy(&session->config, config, sizeof(SftpConfig));
    
    // Connexion TCP avec retry
    logger_log(LOG_DEBUG, "sftp_manager.c : Tentative de connexion TCP vers %s:%d", config->host, config->port);
    int attempt = 0;
    while (attempt < config->retry_attempts) {
        session->sock = connect_to_server(config->host, config->port, config->connection_timeout);
        if (session->sock != -1) break;
        
        attempt++;
        if (attempt < config->retry_attempts) {
            logger_log(LOG_WARNING, "sftp_manager.c : Tentative %d/%d échouée, retry dans %d secondes", 
                      attempt, config->retry_attempts, config->retry_delay);
            sleep(config->retry_delay);
        }
    }
    
    if (session->sock == -1) {
        logger_log(LOG_ERROR, "sftp_manager.c : Impossible d'établir la connexion TCP après %d tentatives", config->retry_attempts);
        free(session);
        return NULL;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Connexion TCP établie vers %s:%d", config->host, config->port);
    }
    
    // Initialisation de la session SSH
    logger_log(LOG_DEBUG, "sftp_manager.c : Initialisation de la session SSH");
    session->ssh_session = libssh2_session_init();
    if (!session->ssh_session) {
        logger_log(LOG_ERROR, "sftp_manager.c : Échec de l'initialisation de la session SSH");
        close(session->sock);
        free(session);
        return NULL;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Session SSH initialisée");
    }
    
    // Configuration des timeouts SSH
    logger_log(LOG_DEBUG, "sftp_manager.c : Configuration des timeouts SSH");
    libssh2_session_set_timeout(session->ssh_session, config->transfer_timeout * 1000);
    
    // Handshake SSH
    logger_log(LOG_DEBUG, "sftp_manager.c : Handshake SSH vers %s:%d", config->host, config->port);
    if (libssh2_session_handshake(session->ssh_session, session->sock) != 0) {
        logger_log(LOG_ERROR, "sftp_manager.c : Échec du handshake SSH");
        libssh2_session_free(session->ssh_session);
        close(session->sock);
        free(session);
        return NULL;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Handshake SSH réussi");
    }
    
    // Authentification
    logger_log(LOG_DEBUG, "sftp_manager.c : Authentification sur la session SSH");
    int auth_result = -1;
    if (strlen(config->private_key_path) > 0) {
        // Authentification par clé privée
        logger_log(LOG_DEBUG, "sftp_manager.c : Authentification par clé privée");
        auth_result = libssh2_userauth_publickey_fromfile(session->ssh_session, 
                                                         config->username,
                                                         NULL, 
                                                         config->private_key_path,
                                                         config->password);
    } else {
        // Authentification par mot de passe
        logger_log(LOG_DEBUG, "sftp_manager.c : Authentification par mot de passe");
        auth_result = libssh2_userauth_password(session->ssh_session, 
                                               config->username, 
                                               config->password);
    }
    
    if (auth_result != 0) {
        logger_log(LOG_ERROR, "sftp_manager.c : Échec de l'authentification pour l'utilisateur %s", config->username);
        libssh2_session_disconnect(session->ssh_session, "Authentification échouée");
        libssh2_session_free(session->ssh_session);
        close(session->sock);
        free(session);
        return NULL;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Authentification réussie pour l'utilisateur %s", config->username);
    }
    
    // Initialisation de la session SFTP
    logger_log(LOG_DEBUG, "sftp_manager.c : Initialisation de la session SFTP");
    session->sftp_session = libssh2_sftp_init(session->ssh_session);
    if (!session->sftp_session) {
        logger_log(LOG_ERROR, "sftp_manager.c : Échec de l'initialisation de la session SFTP");
        libssh2_session_disconnect(session->ssh_session, "Échec SFTP");
        libssh2_session_free(session->ssh_session);
        close(session->sock);
        free(session);
        return NULL;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Session SFTP initialisée avec succès");
    }

    logger_log(LOG_INFO, "sftp_manager.c : Session SFTP établie avec succès vers %s", config->host);
    return session;
}

void sftp_session_cleanup(SftpSession *session) {
    logger_log(LOG_DEBUG, "sftp_manager.c : Nettoyage de la session SFTP");
    if (!session) return;
    logger_log(LOG_DEBUG, "sftp_manager.c : Session SFTP non nulle, nettoyage en cours...");
    logger_log(LOG_DEBUG, "sftp_manager.c : Fermeture de la session SFTP");
    
    if (session->sftp_session) {
        libssh2_sftp_shutdown(session->sftp_session);
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Session SFTP déjà fermée ou non initialisée");
    }
    
    if (session->ssh_session) {
        libssh2_session_disconnect(session->ssh_session, "Session terminée");
        libssh2_session_free(session->ssh_session);
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Session SSH déjà fermée ou non initialisée");
    }
    
    if (session->sock != -1) {
        close(session->sock);
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Socket déjà fermé ou non initialisé");
    }
    
    free(session);
    logger_log(LOG_INFO, "sftp_manager.c : Session SFTP fermée");
}

bool sftp_file_exists(SftpSession *session, const char *remote_path) {
    if (!session || !remote_path) return false;
    
    LIBSSH2_SFTP_ATTRIBUTES attrs;
    int result = libssh2_sftp_stat(session->sftp_session, remote_path, &attrs);
    
    if (result == 0) {
        logger_log(LOG_DEBUG, "sftp_manager.c : Fichier %s existe sur le serveur", remote_path);
        return true;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Fichier %s n'existe pas sur le serveur", remote_path);
        return false;
    }
}

int sftp_download_file(SftpSession *session, const char *remote_path, const char *local_path) {
    if (!session || !remote_path || !local_path) {
        logger_log(LOG_ERROR, "sftp_manager.c : Paramètres invalides pour le téléchargement");
        return -1;
    }

    // Validation du chemin local pour prévenir les attaques de path traversal
    if (validate_file_path(local_path) != 0) {
        logger_log(LOG_ERROR, "sftp_manager.c : Chemin local invalide ou dangereux: %s", local_path);
        fprintf(stderr, "ERREUR SÉCURITÉ: Chemin local invalide dans sftp_download_file: %s\n", local_path);
        return -1;
    }

    logger_log(LOG_DEBUG, "sftp_manager.c : paramètres de téléchargement ok");
    
    logger_log(LOG_INFO, "sftp_manager.c : Téléchargement de %s vers %s", remote_path, local_path);
    
    // Ouvrir le fichier distant
    LIBSSH2_SFTP_HANDLE *remote_file = libssh2_sftp_open(session->sftp_session, remote_path, 
                                                         LIBSSH2_FXF_READ, 0);
    if (!remote_file) {
        logger_log(LOG_ERROR, "sftp_manager.c : Impossible d'ouvrir le fichier distant %s/%s", remote_path, remote_file);
        return -1;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Fichier distant %s/%s ouvert avec succès", remote_path, remote_file);
    }
    
    // Créer le fichier local temporaire
    char temp_path[1024];
    snprintf(temp_path, sizeof(temp_path), "%s.tmp", local_path);

    // Validation du chemin temporaire pour prévenir les attaques de path traversal
    if (validate_file_path(temp_path) != 0) {
        logger_log(LOG_ERROR, "sftp_manager.c : Chemin temporaire invalide ou dangereux: %s", temp_path);
        fprintf(stderr, "ERREUR SÉCURITÉ: Chemin temporaire invalide dans sftp_download_file: %s\n", temp_path);
        libssh2_sftp_close(remote_file);
        return -1;
    }

    logger_log(LOG_DEBUG, "sftp_manager.c : Création du fichier temporaire %s", temp_path);
    
    FILE *local_file = fopen(temp_path, "wb");
    if (!local_file) {
        logger_log(LOG_ERROR, "sftp_manager.c : Impossible de créer le fichier local %s: %s", 
                  temp_path, strerror(errno));
        libssh2_sftp_close(remote_file);
        return -1;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Fichier local temporaire %s créé avec succès", temp_path);
    }
    
    // Transfert des données
    char buffer[8192];
    ssize_t bytes_read;
    size_t total_bytes = 0;
    logger_log(LOG_DEBUG, "sftp_manager.c : Début du transfert de données");

    while ((bytes_read = libssh2_sftp_read(remote_file, buffer, sizeof(buffer))) > 0) {
        if (fwrite(buffer, 1, bytes_read, local_file) != (size_t)bytes_read) {
            logger_log(LOG_ERROR, "sftp_manager.c : Erreur d'écriture dans le fichier local");
            fclose(local_file);
            libssh2_sftp_close(remote_file);
            unlink(temp_path);
            return -1;
        } 
        total_bytes += bytes_read;
    }
    
    // Vérifier les erreurs de lecture
    logger_log(LOG_DEBUG, "sftp_manager.c : Fin du transfert de données, total lu: %zu octets", total_bytes);
    if (bytes_read < 0) {
        logger_log(LOG_ERROR, "sftp_manager.c : Erreur de lecture du fichier distant");
        fclose(local_file);
        libssh2_sftp_close(remote_file);
        unlink(temp_path);
        return -1;
    } else if (bytes_read == 0) {
        logger_log(LOG_DEBUG, "sftp_manager.c : Fin de fichier atteinte (bytes_read == 0) pour %s - Transfert terminé avec succès", remote_path);
    } else {
        logger_log(LOG_ERROR, "sftp_manager.c : Erreur inconnue lors de la lecture du fichier distant - Normalement impossible juste par sécurité.");
        fclose(local_file);
        libssh2_sftp_close(remote_file);
        unlink(temp_path);
        return -1;
    }
    
    // Fermer les fichiers
    fclose(local_file);
    libssh2_sftp_close(remote_file);
    logger_log(LOG_DEBUG, "sftp_manager.c : Fichier local temporaire %s fermé", temp_path);
    
    // Remplacer le fichier original par le temporaire (opération atomique)
    logger_log(LOG_DEBUG, "sftp_manager.c : Remplacement du fichier %s par le temporaire %s", 
              local_path, temp_path);
    if (rename(temp_path, local_path) != 0) {
        logger_log(LOG_ERROR, "sftp_manager.c : Impossible de remplacer %s par %s: %s", 
                  local_path, temp_path, strerror(errno));
        unlink(temp_path);
        return -1;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Fichier %s remplacé par %s avec succès", 
                  local_path, temp_path);
    }
    
    logger_log(LOG_INFO, "sftp_manager.c : Téléchargement réussi: %zu octets transférés", total_bytes);
    return 0;
}

int sftp_sync_remote_version(const SftpConfig *config, const char *local_shared_dir, const char *remote_version_filename) {

    logger_log(LOG_DEBUG, "sftp_manager.c : Lancement de la synchronisation pour %s", remote_version_filename);
    SftpSession *session = sftp_session_init(config);
    if (!session) {
        logger_log(LOG_ERROR, "sftp_manager.c : Impossible d'établir la session SFTP pour la synchronisation");
        return -1;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Session SFTP établie pour la synchronisation");
    }
    
    char remote_path[1024];
    char local_path[1024];

    snprintf(remote_path, sizeof(remote_path), "%s/%s", config->remote_base_path, remote_version_filename);
    snprintf(local_path, sizeof(local_path), "%s/%s", local_shared_dir, remote_version_filename);

    // Validation du chemin local construit pour prévenir les attaques de path traversal
    if (validate_file_path(local_path) != 0) {
        logger_log(LOG_ERROR, "sftp_manager.c : Chemin local construit invalide: %s", local_path);
        fprintf(stderr, "ERREUR SÉCURITÉ: Chemin local invalide dans sftp_sync_remote_version: %s\n", local_path);
        sftp_session_cleanup(session);
        return -1;
    }
    
    int result = 0;
    if (sftp_file_exists(session, remote_path)) {
        logger_log(LOG_INFO, "sftp_manager.c : lancement de sftp_download_file...");
        result = sftp_download_file(session, remote_path, local_path);
    } else {
        logger_log(LOG_WARNING, "sftp_manager.c : Fichier de version %s non trouvé sur le serveur", remote_path);
        result = -1;
    }
    
    sftp_session_cleanup(session);
    return result;
}

int sftp_download_deltas(const SftpConfig *config, const char *local_shared_dir, int start_version, int end_version) {
    if (!config->enabled) {
        logger_log(LOG_DEBUG, "sftp_manager.c : SFTP désactivé, pas de téléchargement de deltas");
        return 0;
    }
    
    SftpSession *session = sftp_session_init(config);
    if (!session) {
        logger_log(LOG_ERROR, "sftp_manager.c : Impossible d'établir la session SFTP pour les deltas");
        return -1;
    } else {
        logger_log(LOG_DEBUG, "sftp_manager.c : Session SFTP établie pour le téléchargement des deltas");
    }
    
    int result = 0;
    
    for (int i = start_version; i < end_version && result == 0; i++) {
        char delta_filename[256];
        char remote_path[1024];
        char local_path[1024];

        // Vérification du pattern
        if (!config->delta_filename_pattern || strlen(config->delta_filename_pattern) == 0) {
            logger_log(LOG_ERROR, "sftp_manager.c : delta_filename_pattern est vide ou NULL !");
            result = -1;
            break;
        }

        snprintf(delta_filename, sizeof(delta_filename), config->delta_filename_pattern, i, i + 1);

        // Vérification du résultat
        if (strlen(delta_filename) == 0) {
            logger_log(LOG_ERROR, "sftp_manager.c : delta_filename est vide pour i=%d ! Arrêt du téléchargement.", i);
            result = -1;
            break;
        }

        snprintf(remote_path, sizeof(remote_path), "%s/%s", config->remote_base_path, delta_filename);
        snprintf(local_path, sizeof(local_path), "%s/%s", local_shared_dir, delta_filename);

        // Validation du chemin local construit pour prévenir les attaques de path traversal
        if (validate_file_path(local_path) != 0) {
            logger_log(LOG_ERROR, "sftp_manager.c : Chemin local delta invalide: %s", local_path);
            fprintf(stderr, "ERREUR SÉCURITÉ: Chemin local delta invalide: %s\n", local_path);
            result = -1;
            break;
        }

        if (sftp_file_exists(session, remote_path)) {
            logger_log(LOG_INFO, "sftp_manager.c : Téléchargement du delta %s", delta_filename);
            if (sftp_download_file(session, remote_path, local_path) == 0) {
                if (sftp_verify_sha256(session, remote_path, local_path) != 0) {
                    logger_log(LOG_ERROR, "sftp_manager.c : Échec de la vérification SHA-256 pour %s", local_path);
                    result = -1;
                } else {
                    logger_log(LOG_DEBUG, "sftp_manager.c : Delta %s téléchargé et vérifié avec succès", delta_filename);
                }
            } else {
                logger_log(LOG_ERROR, "sftp_manager.c : Échec du téléchargement du delta %s", delta_filename);
                result = -1;
            }
        } else {
            logger_log(LOG_ERROR, "sftp_manager.c : Delta %s non trouvé sur le serveur", delta_filename);
            result = -1;
        }
    }
    
    sftp_session_cleanup(session);
    return result;
}

/**
 * @brief Upload un fichier local vers le serveur SFTP avec renommage automatique
 * @param session Session SFTP active
 * @param local_path Chemin du fichier local à uploader
 * @param original_filename Nom de fichier original (ex: "local_version.json")
 * @param box_serial_number Numéro de série de la box
 * @param remote_renamed_path Buffer pour stocker le chemin distant final (optionnel)
 * @param remote_renamed_path_size Taille du buffer remote_renamed_path
 * @return 0 en cas de succès, -1 en cas d'erreur
 */
int sftp_upload_file_with_rename(SftpSession *session, const char *local_path,
                                 const char *original_filename, const char *box_serial_number,
                                 char *remote_renamed_path, size_t remote_renamed_path_size) {
    if (!session || !local_path || !original_filename || !box_serial_number) {
        logger_log(LOG_ERROR, "sftp_manager.c : Paramètres invalides pour l'upload");
        return -1;
    }

    // Validation du chemin local pour prévenir les attaques de path traversal
    if (validate_file_path(local_path) != 0) {
        logger_log(LOG_ERROR, "sftp_manager.c : Chemin local invalide pour upload: %s", local_path);
        fprintf(stderr, "ERREUR SÉCURITÉ: Chemin local invalide dans sftp_upload_file_with_rename: %s\n", local_path);
        return -1;
    }

    logger_log(LOG_DEBUG, "sftp_manager.c : Paramètres d'upload validés");

    // 1. Chemin temporaire .gz
    char gz_local_path[1024];
    snprintf(gz_local_path, sizeof(gz_local_path), "%s.gz", local_path);

    // Validation du chemin temporaire .gz pour prévenir les attaques de path traversal
    if (validate_file_path(gz_local_path) != 0) {
        logger_log(LOG_ERROR, "sftp_manager.c : Chemin temporaire .gz invalide ou dangereux: %s", gz_local_path);
        fprintf(stderr, "ERREUR SÉCURITÉ: Chemin temporaire .gz invalide dans sftp_upload_file_with_rename: %s\n", gz_local_path);
        return -1;
    }

    // 2. Compression
    logger_log(LOG_DEBUG, "sftp_manager.c : Compression du fichier %s vers %s", local_path, gz_local_path);    
    if (compress_file_in_gzip(local_path, gz_local_path) != 0) {
        logger_log(LOG_ERROR, "sftp_manager.c : Échec de la compression gzip");
        return -1;
    }

    // 3. Génération du timestamp
    logger_log(LOG_DEBUG, "sftp_manager.c : Génération du timestamp");
    time_t now = time(NULL);
    struct tm *tm_info = localtime(&now);
    char timestamp[16];
    strftime(timestamp, sizeof(timestamp), "%Y%m%d_%H%M%S", tm_info);

    // 4. Nom distant avec .gz
    char remote_filename[512];
    logger_log(LOG_DEBUG, "sftp_manager.c : Création du nom distant pour le fichier compressé");
    snprintf(remote_filename, sizeof(remote_filename), "%s_%s_%s.gz",
             timestamp, box_serial_number, original_filename);

    char full_remote_path[1024];
    snprintf(full_remote_path, sizeof(full_remote_path), "%s/%s",
             session->config.remote_base_path, remote_filename);

    // 5. Upload du fichier compressé (.gz)
    logger_log(LOG_DEBUG, "sftp_manager.c : Upload du fichier compressé vers %s", full_remote_path);
    struct stat st;
    if (stat(gz_local_path, &st) != 0) {
        logger_log(LOG_ERROR, "sftp_manager.c : Fichier compressé %s inexistant: %s",
                   gz_local_path, strerror(errno));
        unlink(gz_local_path);
        return -1;
    }

    FILE *local_file = fopen(gz_local_path, "rb");
    if (!local_file) {
        logger_log(LOG_ERROR, "sftp_manager.c : Impossible d'ouvrir %s: %s",
                   gz_local_path, strerror(errno));
        unlink(gz_local_path);
        return -1;
    }

    char temp_remote_path[1024];
    snprintf(temp_remote_path, sizeof(temp_remote_path), "%s.tmp", full_remote_path);
    logger_log(LOG_DEBUG, "sftp_manager.c : Création du fichier temporaire distant %s", temp_remote_path);

    LIBSSH2_SFTP_HANDLE *remote_file = libssh2_sftp_open(session->sftp_session, temp_remote_path,
        LIBSSH2_FXF_WRITE | LIBSSH2_FXF_CREAT | LIBSSH2_FXF_TRUNC,
        LIBSSH2_SFTP_S_IRUSR | LIBSSH2_SFTP_S_IWUSR |
        LIBSSH2_SFTP_S_IRGRP | LIBSSH2_SFTP_S_IROTH);

    logger_log(LOG_DEBUG, "sftp_manager.c : Ouverture du fichier distant temporaire %s", temp_remote_path);
    if (!remote_file) {
        logger_log(LOG_ERROR, "sftp_manager.c : Impossible de créer le fichier distant temporaire %s",
                   temp_remote_path);
        fclose(local_file);
        unlink(gz_local_path);
        return -1;
    }

    char buffer[8192];
    size_t bytes_read;
    size_t total_bytes = 0;
    int result = 0;

    logger_log(LOG_DEBUG, "sftp_manager.c : Début de l'upload du fichier compressé");
    while ((bytes_read = fread(buffer, 1, sizeof(buffer), local_file)) > 0) {
        ssize_t bytes_written = libssh2_sftp_write(remote_file, buffer, bytes_read);
        if (bytes_written < 0 || (size_t)bytes_written != bytes_read) {
            logger_log(LOG_ERROR, "sftp_manager.c : Erreur écriture distante");
            result = -1;
            break;
        }
        total_bytes += bytes_read;
    }

    logger_log(LOG_DEBUG, "sftp_manager.c : Fin de l'upload du fichier compressé, total écrit: %zu octets", total_bytes);
    fclose(local_file);
    libssh2_sftp_close(remote_file);

    logger_log(LOG_DEBUG, "sftp_manager.c : Upload terminé pour %s, total écrit: %zu octets", 
               gz_local_path, total_bytes);
    if (result != 0) {
        libssh2_sftp_unlink(session->sftp_session, temp_remote_path);
        unlink(gz_local_path);
        return -1;
    }

    logger_log(LOG_DEBUG, "sftp_manager.c : Renommage du fichier temporaire distant %s en %s",
               temp_remote_path, full_remote_path);
    if (libssh2_sftp_rename(session->sftp_session, temp_remote_path, full_remote_path) != 0) {
        logger_log(LOG_ERROR, "sftp_manager.c : Impossible de renommer %s -> %s",
                   temp_remote_path, full_remote_path);
        libssh2_sftp_unlink(session->sftp_session, temp_remote_path);
        unlink(gz_local_path);
        return -1;
    }

    logger_log(LOG_DEBUG, "sftp_manager.c : Fichier distant renommé avec succès");
    if (remote_renamed_path && remote_renamed_path_size > 0) {
        strncpy(remote_renamed_path, full_remote_path, remote_renamed_path_size - 1);
        remote_renamed_path[remote_renamed_path_size - 1] = '\0';
    }

    logger_log(LOG_INFO, "sftp_manager.c : Upload gzip terminé (%zu octets)", total_bytes);

    // 6. Nettoyage du fichier temporaire
    logger_log(LOG_DEBUG, "sftp_manager.c : Nettoyage du fichier temporaire %s", gz_local_path);
    unlink(gz_local_path);

    return 0;
}
