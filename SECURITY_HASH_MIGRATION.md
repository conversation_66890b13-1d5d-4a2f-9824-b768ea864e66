# Migration de MD5 vers SHA-256 pour la Vérification d'Intégrité

## Contexte

Le warning Snyk **"Use of Password Hash With Insufficient Computational Effort"** a été détecté sur la ligne 68 du fichier `src/sftp_manager.c` :

```c
if (EVP_DigestInit_ex(ctx, EVP_md5(), NULL) != 1) {
```

## Analyse du Warning

### Faux Positif de Sécurité
- **Contexte réel** : Vérification d'intégrité de fichiers après transfert SFTP
- **Interprétation Snyk** : Hachage de mot de passe (incorrect)
- **Problème réel** : MD5 n'est plus recommandé même pour la vérification d'intégrité

### Vulnérabilités de MD5
- **Collisions** : Possibilité de créer deux fichiers avec le même hash MD5
- **Attaques** : Vulnérable aux attaques de collision depuis 2004
- **Performance** : Plus rapide mais moins sécurisé

## Solution Implémentée : Migration vers SHA-256

### Avantages de SHA-256
- ✅ **Sécurité cryptographique** : Résistant aux collisions
- ✅ **Standard moderne** : Recommandé par NIST et industrie
- ✅ **Performance acceptable** : Bon compromis sécurité/performance
- ✅ **Support universel** : Largement supporté

### Changements Effectués

#### 1. Fonction de Calcul
**Avant (MD5)** :
```c
static int compute_md5(const char *file_path, char *output_hex)
if (EVP_DigestInit_ex(ctx, EVP_md5(), NULL) != 1)
output_hex[32] = '\0';  // MD5 = 32 caractères hex
```

**Après (SHA-256)** :
```c
static int compute_sha256(const char *file_path, char *output_hex)
if (EVP_DigestInit_ex(ctx, EVP_sha256(), NULL) != 1)
output_hex[64] = '\0';  // SHA-256 = 64 caractères hex
```

#### 2. Fonction de Vérification
**Avant** :
```c
int sftp_verify_md5(SftpSession *session, const char *remote_path, const char *local_path)
char expected_md5[33];
char computed_md5[33];
snprintf(remote_md5_path, sizeof(remote_md5_path), "%s.md5", remote_path);
```

**Après** :
```c
int sftp_verify_sha256(SftpSession *session, const char *remote_path, const char *local_path)
char expected_sha256[65];  // 64 caractères + '\0'
char computed_sha256[65];
snprintf(remote_sha256_path, sizeof(remote_sha256_path), "%s.sha256", remote_path);
```

#### 3. Tailles des Buffers
- **MD5** : 32 caractères hexadécimaux + '\0' = 33 bytes
- **SHA-256** : 64 caractères hexadécimaux + '\0' = 65 bytes

#### 4. Extensions de Fichiers
- **Avant** : `.md5` pour les fichiers de hash
- **Après** : `.sha256` pour les fichiers de hash

### Impact sur le Système

#### Côté Serveur SFTP
⚠️ **Action requise** : Les fichiers de hash sur le serveur doivent être migrés :
- Renommer `fichier.csv.md5` → `fichier.csv.sha256`
- Recalculer les hash avec SHA-256 au lieu de MD5

#### Côté Client
✅ **Automatique** : Le client utilise maintenant SHA-256 automatiquement

#### Rétrocompatibilité
❌ **Non compatible** : L'ancienne version MD5 ne fonctionnera plus

## Tests de Validation

### Test Fonctionnel
```bash
# Test de la fonction SHA-256
gcc -o test_sha256 test_sha256.c -lssl -lcrypto
./test_sha256
```

**Résultats** :
- ✅ SHA-256 calculé avec succès
- ✅ Longueur correcte (64 caractères)
- ✅ Format hexadécimal valide
- ✅ Reproductibilité confirmée

### Test de Compilation
```bash
cd build && make clean && make
```
**Résultat** : ✅ Compilation réussie sans erreurs

## Exemple d'Utilisation

### Génération de Hash SHA-256 (côté serveur)
```bash
# Générer le hash SHA-256 d'un fichier
sha256sum demographics_v1_v2.csv.gz > demographics_v1_v2.csv.gz.sha256
```

### Vérification (côté client)
Le client télécharge automatiquement :
1. `demographics_v1_v2.csv.gz` (fichier de données)
2. `demographics_v1_v2.csv.gz.sha256` (hash de vérification)
3. Compare le hash calculé avec le hash attendu

## Sécurité Renforcée

### Avant (MD5)
- 🔴 **Vulnérable** aux attaques de collision
- 🔴 **Détection Snyk** : Warning de sécurité
- 🔴 **Standard obsolète**

### Après (SHA-256)
- 🟢 **Résistant** aux attaques connues
- 🟢 **Aucun warning** de sécurité
- 🟢 **Standard moderne** et recommandé

## Conclusion

La migration de MD5 vers SHA-256 :
- ✅ **Résout** le warning Snyk
- ✅ **Améliore** la sécurité de la vérification d'intégrité
- ✅ **Modernise** le code selon les standards actuels
- ✅ **Maintient** la fonctionnalité de vérification d'intégrité

**Note importante** : Cette migration nécessite une mise à jour coordonnée du serveur SFTP pour utiliser des fichiers `.sha256` au lieu de `.md5`.
