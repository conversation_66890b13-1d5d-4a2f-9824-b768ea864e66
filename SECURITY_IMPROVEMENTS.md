# Améliorations de Sécurité - Path Traversal Protection

## Problèmes Identifiés

Le code présentait plusieurs vulnérabilités de **Path Traversal** identifiées par Snyk :

### 1. Premier cas (RÉSOLU)
- **Source** : `main.c:260` - `Config config = load_config(argv[1]);`
- **Sink** : `update.c:453-455` - `FILE *fp = fopen(file_path, "r");`
- **Risque** : Un attaquant pouvait lire des fichiers arbitraires du système en utilisant des séquences comme `../../../etc/passwd`

### 2. Deuxième cas (RÉSOLU)
- **Source** : `update.c:583` - `char *content = malloc(file_size + 1);`
- **Sink** : `sftp_manager.c:537` - `if (rename(temp_path, local_path) != 0)`
- **Risque** : Un attaquant pouvait manipuler les chemins dans le fichier de configuration pour faire écrire des fichiers dans des répertoires système via les fonctions SFTP

### 3. Troisième cas (RÉSOLU)
- **Source** : Chemins construits dynamiquement dans `apply_demographics_delta()`
- **Sink** : `update.c:404` - `if (rename(tmp_path, csv_path) != 0)`
- **Risque** : Manipulation des chemins de fichiers temporaires lors de l'application des deltas

### 4. Quatrième cas (RÉSOLU)
- **Source** : Chemins construits dynamiquement dans `sftp_upload_file_with_rename()`
- **Sink** : Création de fichiers temporaires `.gz` et `.tmp`
- **Risque** : Manipulation des chemins lors de l'upload de fichiers vers le serveur SFTP

## Solution Implémentée

### 1. Fonction de Validation `validate_file_path()`

Une nouvelle fonction de validation a été ajoutée dans `src/update.c` qui :

- **Vérifie la longueur du chemin** (doit être < PATH_MAX)
- **Détecte les séquences de path traversal multiples** (`../../`, `../../../`, etc.)
- **Valide les caractères de contrôle** (rejette les caractères < 32 sauf tab)
- **Utilise `realpath()`** pour résoudre le chemin canonique
- **Vérifie l'accès aux répertoires système** (`/etc/`, `/root/`, `/usr/`, etc.)

### 2. Fonctions Sécurisées

**Fonctions dans `update.c` :**
- `read_file_content()` - Lecture de fichiers texte
- `read_gz_file_content()` - Lecture de fichiers compressés
- `save_local_version()` - Sauvegarde de fichiers JSON
- `compress_file_in_gzip()` - Compression de fichiers
- `rebuild_demographics_index()` - Reconstruction d'index

**Fonctions dans `sftp_manager.c` :**
- `sftp_download_file()` - Téléchargement de fichiers via SFTP (validation de `local_path` et `temp_path`)
- `sftp_sync_remote_version()` - Synchronisation de fichiers de version
- `sftp_download_deltas()` - Téléchargement de deltas via SFTP
- `sftp_upload_file_with_rename()` - Upload de fichiers avec renommage (validation de `gz_local_path`)

**Fonctions dans `update.c` :**
- `apply_demographics_delta()` - Application de deltas (validation de `csv_path` et `tmp_path`)

### 3. Fonction de Validation Publique

La fonction `validate_file_path()` est maintenant publique (déclarée dans `update.h`) et peut être utilisée par tous les modules du projet.

### 4. Messages d'Erreur

Les tentatives d'attaque sont maintenant :
- **Loggées** dans les logs de l'application
- **Affichées sur stderr** pour un diagnostic immédiat
- **Bloquées** avec un code de retour d'erreur

### 5. Flux de Données Sécurisés

**Premier cas :** `argv[1]` → `load_config()` → `read_file_content()` → `validate_file_path()` → `fopen()`

**Deuxième cas :** Configuration JSON → Chemins extraits → Fonctions SFTP → `validate_file_path()` → `rename()`

**Troisième cas :** Paramètres de fonction → Construction de chemins → `validate_file_path()` → `rename()`

**Quatrième cas :** Chemins d'upload → Construction de fichiers temporaires → `validate_file_path()` → Opérations de fichiers

## Tests de Validation

### ✅ Chemins Valides Acceptés
```bash
./demog_updater ../config/config.json  # Chemin relatif légitime
./demog_updater /path/to/project/config.json  # Chemin absolu dans le projet
```

### ❌ Attaques Bloquées
```bash
./demog_updater ../../../etc/passwd  # Path traversal multiple
./demog_updater /etc/passwd  # Accès direct aux fichiers système
./demog_updater ../../../../../../etc/shadow  # Path traversal extrême
```

## Exemples de Messages d'Erreur

```
ERREUR SÉCURITÉ: Séquences de path traversal multiples détectées dans le chemin: ../../../etc/passwd
ERREUR SÉCURITÉ: Tentative d'accès à un répertoire système: /etc/passwd
```

## Impact sur les Performances

- **Minimal** : La validation ajoute quelques vérifications de chaînes et un appel à `realpath()`
- **Sécurité renforcée** : Protection complète contre les attaques de path traversal
- **Compatibilité** : Les chemins légitimes continuent de fonctionner normalement

## Recommandations Supplémentaires

1. **Audit régulier** : Vérifier périodiquement les logs pour détecter les tentatives d'attaque
2. **Tests de sécurité** : Inclure des tests de path traversal dans la suite de tests
3. **Principe du moindre privilège** : Exécuter l'application avec des privilèges minimaux
4. **Chroot/containers** : Considérer l'utilisation de conteneurs pour isoler l'application

## Conformité

Cette implémentation respecte les bonnes pratiques de sécurité :
- **OWASP Top 10** - Protection contre les vulnérabilités de path traversal
- **CWE-22** - Improper Limitation of a Pathname to a Restricted Directory
- **SANS Top 25** - Protection contre les vulnérabilités d'injection de chemin
