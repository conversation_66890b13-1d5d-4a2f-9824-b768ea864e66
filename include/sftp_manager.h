#ifndef SFTP_MANAGER_H
#define SFTP_MANAGER_H

#include <stdbool.h>
#include <stddef.h>

// Structure pour la configuration SFTP
typedef struct {
    bool enabled;
    char host[256];
    int port;
    char username[64];
    char password[256];
    char private_key_path[512];
    char remote_base_path[512];
    int connection_timeout;
    int transfer_timeout;
    int retry_attempts;
    int retry_delay;
    char delta_filename_pattern[256];
} SftpConfig;

// Structure pour la session SFTP
typedef struct SftpSession SftpSession;

/**
 * @brief Initialise une session SFTP
 * @param config Configuration SFTP
 * @return Pointeur vers la session ou NULL en cas d'erreur
 */
SftpSession* sftp_session_init(const SftpConfig *config);

/**
 * @brief Ferme et libère une session SFTP
 * @param session Session à fermer
 */
void sftp_session_cleanup(SftpSession *session);

/**
 * @brief Télécharge un fichier depuis le serveur SFTP
 * @param session Session SFTP active
 * @param remote_path Chemin du fichier sur le serveur
 * @param local_path Chemin local de destination
 * @return 0 en cas de succès, -1 en cas d'erreur
 */
int sftp_download_file(SftpSession *session, const char *remote_path, const char *local_path);

/**
 * @brief Télécharge et vérifie le MD5 d’un fichier distant.
 * @param session Session SFTP active
 * @param remote_path Chemin du fichier distant (sans extension .md5)
 * @param local_path Chemin local du fichier téléchargé
 * @return 0 si l’intégrité est confirmée, -1 si échec
 */
int sftp_verify_sha256(SftpSession *session, const char *remote_path, const char *local_path);

/**
 * @brief Vérifie si un fichier existe sur le serveur SFTP
 * @param session Session SFTP active
 * @param remote_path Chemin du fichier sur le serveur
 * @return true si le fichier existe, false sinon
 */
bool sftp_file_exists(SftpSession *session, const char *remote_path);

/**
 * @brief Télécharge le fichier remote_version.json avec gestion des erreurs
 * @param config Configuration SFTP
 * @param local_shared_dir Répertoire local partagé
 * @param remote_version_filename Nom du fichier de version distante
 * @return 0 en cas de succès, -1 en cas d'erreur
 */
int sftp_sync_remote_version(const SftpConfig *config, const char *local_shared_dir, const char *remote_version_filename);

/**
 * @brief Télécharge les fichiers delta nécessaires pour la mise à jour
 * @param config Configuration SFTP
 * @param local_shared_dir Répertoire local partagé
 * @param start_version Version de départ
 * @param end_version Version d'arrivée
 * @return 0 en cas de succès, -1 en cas d'erreur
 */
int sftp_download_deltas(const SftpConfig *config, const char *local_shared_dir, int start_version, int end_version);

/**
 * @brief Upload un fichier local vers le serveur SFTP avec renommage automatique
 * @param session Session SFTP active
 * @param local_path Chemin du fichier local à uploader
 * @param original_filename Nom de fichier original (ex: "local_version.json")
 * @param box_serial_number Numéro de série de la box
 * @param remote_renamed_path Buffer pour stocker le chemin distant final (optionnel)
 * @param remote_renamed_path_size Taille du buffer remote_renamed_path
 * @return 0 en cas de succès, -1 en cas d'erreur
 */
int sftp_upload_file_with_rename(SftpSession *session, const char *local_path,
                                 const char *original_filename, const char *box_serial_number,
                                 char *remote_renamed_path, size_t remote_renamed_path_size);



                                 
#endif // SFTP_MANAGER_H